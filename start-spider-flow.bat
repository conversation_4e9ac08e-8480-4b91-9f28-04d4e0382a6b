@echo off
echo Starting Spider-Flow with optimized JVM settings...

REM
set JAVA_OPTS=-Dpolyglot.js.nashorn-compat=true ^
-Dpolyglot.js.allowHostAccess=true ^
-Dpolyglot.js.allowHostClassLookup=true ^
-Dpolyglot.engine.WarnInterpreterOnly=false ^
-Dfile.encoding=UTF-8 ^
-Dconsole.encoding=UTF-8

echo JVM Options: %JAVA_OPTS%
echo.

REM
mvn spring-boot:run -pl spider-flow-web -Dspring-boot.run.jvmArguments="%JAVA_OPTS%"

pause
