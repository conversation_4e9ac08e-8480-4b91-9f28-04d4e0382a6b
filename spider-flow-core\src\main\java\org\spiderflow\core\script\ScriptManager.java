package org.spiderflow.core.script;

// {{ AURA-X: Modify - 替换 Nashorn 导入为 GraalVM JavaScript 导入. Approval: 寸止(ID:1737188400). }}
// {{ Source: GraalVM JavaScript Migration Guide }}
import org.graalvm.polyglot.Value;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.spiderflow.core.expression.ExpressionTemplate;
import org.spiderflow.core.expression.ExpressionTemplateContext;

import javax.script.Invocable;
import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import javax.script.ScriptException;
import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.locks.ReadWriteLock;
import java.util.concurrent.locks.ReentrantReadWriteLock;

public class ScriptManager {

    private static Logger logger = LoggerFactory.getLogger(ScriptManager.class);

    private static ScriptEngine scriptEngine;

    private static Set<String> functions = new HashSet<>();

    private static ReadWriteLock lock = new ReentrantReadWriteLock();

    public static void setScriptEngine(ScriptEngine engine){
        scriptEngine = engine;
        StringBuffer script = new StringBuffer();
        script.append("var ExpressionTemplate = Java.type('")
                .append(ExpressionTemplate.class.getName())
                .append("');")
                .append("var ExpressionTemplateContext = Java.type('")
                .append(ExpressionTemplateContext.class.getName())
                .append("');")
                .append("function _eval(expression) {")
                .append("return ExpressionTemplate.create(expression).render(ExpressionTemplateContext.get());")
                .append("}");
        try {
            scriptEngine.eval(script.toString());
        } catch (ScriptException e) {
            logger.error("注册_eval函数失败",e);
        }
    }

    public static void clearFunctions(){
        functions.clear();
    }

    public static ScriptEngine createEngine(){
        // {{ AURA-X: Modify - 切换到 GraalVM JavaScript 引擎并配置 Nashorn 兼容性. Approval: 寸止(ID:1737188400). }}
        // {{ Source: GraalVM JavaScript Migration Guide }}
        ScriptEngine engine = new ScriptEngineManager().getEngineByName("graal.js");

        // 配置 Nashorn 兼容性模式
        if (engine != null) {
            try {
                // 通过系统属性启用 Nashorn 兼容性（如果支持）
                System.setProperty("polyglot.js.nashorn-compat", "true");
                System.setProperty("polyglot.js.ecmascript-version", "5");
                logger.info("GraalVM JavaScript 引擎已启用 Nashorn 兼容性模式");
            } catch (Exception e) {
                logger.warn("无法设置 Nashorn 兼容性模式，将使用默认配置", e);
            }
        }

        return engine;
    }

    public static void lock(){
        lock.writeLock().lock();
    }

    public static void unlock(){
        lock.writeLock().unlock();
    }

    public static void registerFunction(ScriptEngine engine,String functionName,String parameters,String script){
        try {
            engine.eval(concatScript(functionName,parameters,script));
            functions.add(functionName);
            logger.info("注册自定义函数{}成功",functionName);
        } catch (ScriptException e) {
            logger.warn("注册自定义函数{}失败",functionName,e);
        }
    }

    private static String concatScript(String functionName,String parameters,String script){
        StringBuffer scriptBuffer = new StringBuffer();
        scriptBuffer.append("function ")
                .append(functionName)
                .append("(")
                .append(parameters == null ? "" : parameters)
                .append("){")
                .append(script)
                .append("}");
        return scriptBuffer.toString();
    }

    public static boolean containsFunction(String functionName){
        try {
            lock.readLock().lock();
            return functions.contains(functionName);
        } finally {
            lock.readLock().unlock();
        }
    }

    public static void validScript(String functionName,String parameters,String script) throws Exception {
        // {{ AURA-X: Modify - 切换到 GraalVM JavaScript 引擎. Approval: 寸止(ID:1737188400). }}
        new ScriptEngineManager().getEngineByName("graal.js").eval(concatScript(functionName,parameters,script));
    }

    public static Object eval(ExpressionTemplateContext context, String functionName, Object ... args) throws ScriptException, NoSuchMethodException {
        if("_eval".equals(functionName)){
            if(args == null || args.length != 1){
                throw new ScriptException("_eval必须要有一个参数");
            }else{
                return ExpressionTemplate.create(args[0].toString()).render(context);
            }
        }
        if(scriptEngine == null){
            throw new NoSuchMethodException(functionName);
        }
        try{
            lock.readLock().lock();
            return convertObject(((Invocable) scriptEngine).invokeFunction(functionName, args));
        } finally{
            lock.readLock().unlock();
        }
    }

    private static Object convertObject(Object object){
        // {{ AURA-X: Modify - 重构 ScriptObjectMirror 为 GraalVM Value. Approval: 寸止(ID:1737188400). }}
        // {{ Source: GraalVM JavaScript Migration Guide - ScriptObjectMirror Objects section }}
        if(object instanceof Value){
            Value value = (Value) object;
            if(value.hasArrayElements()){
                // 处理数组类型
                long size = value.getArraySize();
                Object[] array = new Object[(int)size];
                for (int i = 0; i < size; i++) {
                    array[i] = convertObject(value.getArrayElement(i));
                }
                return array;
            }else if(value.isDate()){
                // 处理日期类型
                return value.asDate();
            }else if(value.isNumber()){
                // 处理数字类型
                if(value.fitsInLong()){
                    return value.asLong();
                }else{
                    return value.asDouble();
                }
            }else if(value.isString()){
                // 处理字符串类型
                return value.asString();
            }else if(value.isBoolean()){
                // 处理布尔类型
                return value.asBoolean();
            }
            // 其它类型保持原样或转换为 Map
            // GraalVM Value 对象可以直接作为 Map 使用
        }
        return object;
    }
}
