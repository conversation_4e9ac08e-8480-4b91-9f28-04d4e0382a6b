<?xml version="1.0"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>org.spiderflow</groupId>
		<artifactId>spider-flow</artifactId>
		<version>0.5.0</version>
	</parent>
	<artifactId>spider-flow-web</artifactId>
	<name>spider-flow-web</name>
	<url>https://gitee.com/jmxd/spider-flow/tree/master/spider-flow-web</url>
	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
	</properties>
	<dependencies>
		<!-- 核心模块依赖 -->
		<dependency>
			<groupId>org.spiderflow</groupId>
			<artifactId>spider-flow-core</artifactId>
		</dependency>
		<!--
		以下扩展模块依赖已移除，因为对应的模块在当前项目中不存在：
		- spider-flow-redis: Redis 支持
		- spider-flow-mongodb: MongoDB 支持
		- spider-flow-selenium: Selenium 浏览器自动化
		- spider-flow-ocr: OCR 文字识别
		- spider-flow-oss: 对象存储服务
		- spider-flow-mailbox: 邮箱功能

		如需要这些功能，请：
		1. 创建对应的模块项目
		2. 或者寻找这些模块的源码
		3. 或者使用第三方替代方案
		-->
	</dependencies>
	<build>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<configuration>
					<finalName>spider-flow</finalName>
					<mainClass>org.spiderflow.SpiderApplication</mainClass>
				</configuration>
			</plugin>
		</plugins>
	</build>
</project>
