<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <springProperty scope="context" name="LOG_LEVEL" source="logging.level.root" defaultValue="DEBUG"/>
    <springProperty scope="context" name="WORKSPACE" source="spider.workspace" defaultValue="/data/spider/logs"/>
    <!-- 控制台输出 -->
    <appender name="Stdout" class="ch.qos.logback.core.ConsoleAppender">
        <!-- 启用颜色支持 -->
        <withJansi>true</withJansi>
        <!-- 设置输出目标为标准输出 -->
        <target>System.out</target>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <!--格式化输出：%d表示日期，%thread表示线程名，%-5level：级别从左显示5个字符宽度%msg：日志消息，%n是换行符-->
            <!-- 使用颜色高亮：%highlight()用于高亮级别，%cyan()用于线程名，%magenta()用于logger名 -->
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} %cyan([%thread]) %highlight(%-5level) %magenta(%logger{36}) - %msg%n</pattern>
            <!-- 设置字符编码 -->
            <charset>UTF-8</charset>
        </encoder>
        <!-- 过滤器：只输出INFO及以上级别到控制台 -->
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>INFO</level>
        </filter>
    </appender>
    <!-- 输出日志文件 -->
    <appender name="File"  class="org.spiderflow.logback.SpiderFlowFileAppender">
        <file>${WORKSPACE}/logs/spider-flow.log</file>
        <append>true</append>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <!--格式化输出：%d表示日期，%thread表示线程名，%-5level：级别从左显示5个字符宽度%msg：日志消息，%n是换行符-->
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} %-5level %logger{50} - %msg%n</pattern>
        </encoder>
    </appender>
    <!-- WebSocket输出日志 -->
    <appender name="WebSocket" class="org.spiderflow.logback.SpiderFlowWebSocketAppender"/>
    <!-- 日志输出级别 -->
    <root level="${LOG_LEVEL}">
        <appender-ref ref="Stdout" />
        <appender-ref ref="File" />
        <appender-ref ref="WebSocket" />
    </root>
</configuration>