2025-08-17 18:31:56.154 INFO  org.spiderflow.SpiderApplication - Starting SpiderApplication using Java 21.0.8 on XIAO with PID 15124 (C:\Users\<USER>\Downloads\spider-flow-0.5.0\spider-flow-web\target\classes started by 21653 in C:\Users\<USER>\Downloads\spider-flow-0.5.0)
2025-08-17 18:31:56.161 INFO  org.spiderflow.SpiderApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-17 18:31:57.145 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8088 (http)
2025-08-17 18:31:57.152 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8088"]
2025-08-17 18:31:57.159 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-17 18:31:57.159 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-08-17 18:31:57.251 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-17 18:31:57.252 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1024 ms
2025-08-17 18:31:57.349 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-08-17 18:31:57.446 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-08-17 18:31:57.640 ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[org.spiderflow.core.mapper.SpiderFlowMapper.resetNextExecuteTime] is ignored, because it exists, maybe from xml file
2025-08-17 18:31:57.931 INFO  org.spiderflow.core.script.ScriptManager - 成功创建 JavaScript 引擎: graal.js
2025-08-17 18:31:57.932 INFO  org.spiderflow.core.script.ScriptManager - GraalVM JavaScript 引擎已启用 Nashorn 兼容性模式
2025-08-17 18:31:58.697 WARN  org.spiderflow.core.script.ScriptManager - GraalVM JavaScript 引擎注册 _eval 函数失败，将跳过此功能: ReferenceError: Java is not defined in <eval> at line number 1 at column number 14
2025-08-17 18:31:58.783 INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
2025-08-17 18:31:58.791 INFO  org.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-08-17 18:31:58.791 INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v.2.3.2 created.
2025-08-17 18:31:58.792 INFO  org.quartz.simpl.RAMJobStore - RAMJobStore initialized.
2025-08-17 18:31:58.792 INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-08-17 18:31:58.792 INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-08-17 18:31:58.792 INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.3.2
2025-08-17 18:31:58.792 INFO  org.quartz.core.QuartzScheduler - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@7b3085a3
2025-08-17 18:31:59.063 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-08-17 18:31:59.204 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8088"]
2025-08-17 18:31:59.217 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8088 (http) with context path ''
2025-08-17 18:31:59.218 INFO  o.s.scheduling.quartz.SchedulerFactoryBean - Starting Quartz Scheduler now
2025-08-17 18:31:59.218 INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-08-17 18:31:59.225 INFO  org.spiderflow.SpiderApplication - Started SpiderApplication in 3.567 seconds (JVM running for 4.365)
2025-08-17 18:33:36.048 INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-08-17 18:33:36.065 INFO  o.s.scheduling.quartz.SchedulerFactoryBean - Shutting down Quartz Scheduler
2025-08-17 18:33:36.065 INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-08-17 18:33:36.066 INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-08-17 18:33:36.066 INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-08-17 18:33:36.066 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-08-17 18:33:36.072 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-08-17 18:33:40.940 INFO  org.spiderflow.SpiderApplication - Starting SpiderApplication using Java 21.0.8 on XIAO with PID 14916 (C:\Users\<USER>\Downloads\spider-flow-0.5.0\spider-flow-web\target\classes started by 21653 in C:\Users\<USER>\Downloads\spider-flow-0.5.0)
2025-08-17 18:33:40.948 INFO  org.spiderflow.SpiderApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-17 18:33:42.098 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8088 (http)
2025-08-17 18:33:42.108 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8088"]
2025-08-17 18:33:42.109 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-17 18:33:42.109 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-08-17 18:33:42.194 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-17 18:33:42.194 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1194 ms
2025-08-17 18:33:42.300 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-08-17 18:33:42.382 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-08-17 18:33:42.939 INFO  org.spiderflow.core.script.ScriptManager - 成功创建 JavaScript 引擎: graal.js
2025-08-17 18:33:42.941 INFO  org.spiderflow.core.script.ScriptManager - GraalVM JavaScript 引擎已启用 Nashorn 兼容性模式
2025-08-17 18:33:43.843 WARN  org.spiderflow.core.script.ScriptManager - GraalVM JavaScript 引擎注册 _eval 函数失败，将跳过此功能: ReferenceError: Java is not defined in <eval> at line number 1 at column number 14
2025-08-17 18:33:43.927 INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
2025-08-17 18:33:43.935 INFO  org.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-08-17 18:33:43.935 INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v.2.3.2 created.
2025-08-17 18:33:43.935 INFO  org.quartz.simpl.RAMJobStore - RAMJobStore initialized.
2025-08-17 18:33:43.936 INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-08-17 18:33:43.936 INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-08-17 18:33:43.936 INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.3.2
2025-08-17 18:33:43.936 INFO  org.quartz.core.QuartzScheduler - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@5a2035e1
2025-08-17 18:33:44.196 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-08-17 18:33:44.337 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8088"]
2025-08-17 18:33:44.394 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8088 (http) with context path ''
2025-08-17 18:33:44.395 INFO  o.s.scheduling.quartz.SchedulerFactoryBean - Starting Quartz Scheduler now
2025-08-17 18:33:44.395 INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-08-17 18:33:44.404 INFO  org.spiderflow.SpiderApplication - Started SpiderApplication in 4.018 seconds (JVM running for 4.752)
2025-08-17 18:35:41.396 INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-08-17 18:35:41.418 INFO  o.s.scheduling.quartz.SchedulerFactoryBean - Shutting down Quartz Scheduler
2025-08-17 18:35:41.418 INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-08-17 18:35:41.418 INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-08-17 18:35:41.418 INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-08-17 18:35:41.419 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-08-17 18:35:41.427 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-08-17 18:35:43.310 INFO  org.spiderflow.SpiderApplication - Starting SpiderApplication using Java 21.0.8 on XIAO with PID 18440 (C:\Users\<USER>\Downloads\spider-flow-0.5.0\spider-flow-web\target\classes started by 21653 in C:\Users\<USER>\Downloads\spider-flow-0.5.0)
2025-08-17 18:35:43.316 INFO  org.spiderflow.SpiderApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-17 18:35:44.326 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8088 (http)
2025-08-17 18:35:44.333 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8088"]
2025-08-17 18:35:44.334 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-17 18:35:44.334 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-08-17 18:35:44.429 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-17 18:35:44.429 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1070 ms
2025-08-17 18:35:44.513 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-08-17 18:35:44.586 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-08-17 18:35:45.326 INFO  org.spiderflow.core.script.ScriptManager - 成功创建 JavaScript 引擎: graal.js
2025-08-17 18:35:45.327 INFO  org.spiderflow.core.script.ScriptManager - GraalVM JavaScript 引擎已启用 Nashorn 兼容性模式
2025-08-17 18:35:46.241 WARN  org.spiderflow.core.script.ScriptManager - GraalVM JavaScript 引擎注册 _eval 函数失败，将跳过此功能: ReferenceError: Java is not defined in <eval> at line number 1 at column number 14
2025-08-17 18:35:46.325 INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
2025-08-17 18:35:46.334 INFO  org.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-08-17 18:35:46.334 INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v.2.3.2 created.
2025-08-17 18:35:46.335 INFO  org.quartz.simpl.RAMJobStore - RAMJobStore initialized.
2025-08-17 18:35:46.335 INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-08-17 18:35:46.335 INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-08-17 18:35:46.335 INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.3.2
2025-08-17 18:35:46.335 INFO  org.quartz.core.QuartzScheduler - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@5e681097
2025-08-17 18:35:46.654 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-08-17 18:35:46.796 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8088"]
2025-08-17 18:35:46.809 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8088 (http) with context path ''
2025-08-17 18:35:46.810 INFO  o.s.scheduling.quartz.SchedulerFactoryBean - Starting Quartz Scheduler now
2025-08-17 18:35:46.810 INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-08-17 18:35:46.817 INFO  org.spiderflow.SpiderApplication - Started SpiderApplication in 3.982 seconds (JVM running for 4.711)
2025-08-17 18:35:58.611 INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-08-17 18:35:58.638 INFO  o.s.scheduling.quartz.SchedulerFactoryBean - Shutting down Quartz Scheduler
2025-08-17 18:35:58.638 INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-08-17 18:35:58.638 INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-08-17 18:35:58.638 INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-08-17 18:35:58.639 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-08-17 18:35:58.645 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-08-17 18:36:07.901 INFO  org.spiderflow.SpiderApplication - Starting SpiderApplication using Java 21.0.8 on XIAO with PID 19068 (C:\Users\<USER>\Downloads\spider-flow-0.5.0\spider-flow-web\target\classes started by 21653 in C:\Users\<USER>\Downloads\spider-flow-0.5.0)
2025-08-17 18:36:07.906 INFO  org.spiderflow.SpiderApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-17 18:36:08.881 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8088 (http)
2025-08-17 18:36:08.888 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8088"]
2025-08-17 18:36:08.889 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-17 18:36:08.889 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-08-17 18:36:08.985 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-17 18:36:08.985 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1037 ms
2025-08-17 18:36:09.071 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-08-17 18:36:09.152 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-08-17 18:36:09.641 INFO  org.spiderflow.core.script.ScriptManager - 成功创建 JavaScript 引擎: graal.js
2025-08-17 18:36:09.642 INFO  org.spiderflow.core.script.ScriptManager - GraalVM JavaScript 引擎已启用 Nashorn 兼容性模式
2025-08-17 18:36:11.245 WARN  org.spiderflow.core.script.ScriptManager - GraalVM JavaScript 引擎注册 _eval 函数失败，将跳过此功能: ReferenceError: Java is not defined in <eval> at line number 1 at column number 14
2025-08-17 18:36:11.547 INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
2025-08-17 18:36:11.604 INFO  org.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-08-17 18:36:11.605 INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v.2.3.2 created.
2025-08-17 18:36:11.607 INFO  org.quartz.simpl.RAMJobStore - RAMJobStore initialized.
2025-08-17 18:36:11.609 INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-08-17 18:36:11.610 INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-08-17 18:36:11.610 INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.3.2
2025-08-17 18:36:11.610 INFO  org.quartz.core.QuartzScheduler - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@4348fa35
2025-08-17 18:36:12.840 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-08-17 18:36:13.345 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8088"]
2025-08-17 18:36:13.406 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8088 (http) with context path ''
2025-08-17 18:36:13.407 INFO  o.s.scheduling.quartz.SchedulerFactoryBean - Starting Quartz Scheduler now
2025-08-17 18:36:13.408 INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-08-17 18:36:13.423 INFO  org.spiderflow.SpiderApplication - Started SpiderApplication in 6.001 seconds (JVM running for 6.885)
2025-08-17 18:36:34.808 INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-08-17 18:36:34.822 INFO  o.s.scheduling.quartz.SchedulerFactoryBean - Shutting down Quartz Scheduler
2025-08-17 18:36:34.822 INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-08-17 18:36:34.822 INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-08-17 18:36:34.822 INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-08-17 18:36:34.823 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-08-17 18:36:34.829 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-08-17 18:36:50.219 INFO  org.spiderflow.SpiderApplication - Starting SpiderApplication using Java 21.0.8 on XIAO with PID 12132 (C:\Users\<USER>\Downloads\spider-flow-0.5.0\spider-flow-web\target\classes started by 21653 in C:\Users\<USER>\Downloads\spider-flow-0.5.0)
2025-08-17 18:36:50.229 INFO  org.spiderflow.SpiderApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-17 18:36:51.043 WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.CannotLoadBeanClassException: Error loading class [org.spiderflow.core.utils.ExecutorsUtils] for bean with name 'executorsUtils' defined in file [C:\Users\<USER>\Downloads\spider-flow-0.5.0\spider-flow-core\target\classes\org\spiderflow\core\utils\ExecutorsUtils.class]: problem with class file or dependent class; nested exception is java.lang.ClassFormatError: Extra bytes at the end of class file org/spiderflow/core/utils/ExecutorsUtils
2025-08-17 18:36:51.049 INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-08-17 18:36:51.065 ERROR org.springframework.boot.SpringApplication - Application run failed
org.springframework.beans.factory.CannotLoadBeanClassException: Error loading class [org.spiderflow.core.utils.ExecutorsUtils] for bean with name 'executorsUtils' defined in file [C:\Users\<USER>\Downloads\spider-flow-0.5.0\spider-flow-core\target\classes\org\spiderflow\core\utils\ExecutorsUtils.class]: problem with class file or dependent class; nested exception is java.lang.ClassFormatError: Extra bytes at the end of class file org/spiderflow/core/utils/ExecutorsUtils
	at org.springframework.beans.factory.support.AbstractBeanFactory.resolveBeanClass(AbstractBeanFactory.java:1559)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.determineTargetType(AbstractAutowireCapableBeanFactory.java:704)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.predictBeanType(AbstractAutowireCapableBeanFactory.java:674)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getType(AbstractBeanFactory.java:722)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.findMergedAnnotationOnBean(DefaultListableBeanFactory.java:751)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.findAnnotationOnBean(DefaultListableBeanFactory.java:744)
	at org.springframework.boot.sql.init.dependency.AnnotationDependsOnDatabaseInitializationDetector.detect(AnnotationDependsOnDatabaseInitializationDetector.java:36)
	at org.springframework.boot.sql.init.dependency.DatabaseInitializationDependencyConfigurer$DependsOnDatabaseInitializationPostProcessor.detectDependsOnInitializationBeanNames(DatabaseInitializationDependencyConfigurer.java:148)
	at org.springframework.boot.sql.init.dependency.DatabaseInitializationDependencyConfigurer$DependsOnDatabaseInitializationPostProcessor.postProcessBeanFactory(DatabaseInitializationDependencyConfigurer.java:111)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanFactoryPostProcessors(PostProcessorRegistrationDelegate.java:325)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanFactoryPostProcessors(PostProcessorRegistrationDelegate.java:191)
	at org.springframework.context.support.AbstractApplicationContext.invokeBeanFactoryPostProcessors(AbstractApplicationContext.java:756)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:572)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:732)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:409)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1289)
	at org.spiderflow.SpiderApplication.main(SpiderApplication.java:25)
Caused by: java.lang.ClassFormatError: Extra bytes at the end of class file org/spiderflow/core/utils/ExecutorsUtils
	at java.base/java.lang.ClassLoader.defineClass1(Native Method)
	at java.base/java.lang.ClassLoader.defineClass(ClassLoader.java:1027)
	at java.base/java.security.SecureClassLoader.defineClass(SecureClassLoader.java:150)
	at java.base/jdk.internal.loader.BuiltinClassLoader.defineClass(BuiltinClassLoader.java:862)
	at java.base/jdk.internal.loader.BuiltinClassLoader.findClassOnClassPathOrNull(BuiltinClassLoader.java:760)
	at java.base/jdk.internal.loader.BuiltinClassLoader.loadClassOrNull(BuiltinClassLoader.java:681)
	at java.base/jdk.internal.loader.BuiltinClassLoader.loadClass(BuiltinClassLoader.java:639)
	at java.base/jdk.internal.loader.ClassLoaders$AppClassLoader.loadClass(ClassLoaders.java:188)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:534)
	at java.base/java.lang.Class.forName(Class.java:513)
	at org.springframework.util.ClassUtils.forName(ClassUtils.java:291)
	at org.springframework.beans.factory.support.AbstractBeanDefinition.resolveBeanClass(AbstractBeanDefinition.java:469)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doResolveBeanClass(AbstractBeanFactory.java:1621)
	at org.springframework.beans.factory.support.AbstractBeanFactory.resolveBeanClass(AbstractBeanFactory.java:1548)
	... 19 common frames omitted
