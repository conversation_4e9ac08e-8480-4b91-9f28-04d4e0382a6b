2025-08-17 18:31:56.154 INFO  org.spiderflow.SpiderApplication - Starting SpiderApplication using Java 21.0.8 on XIAO with PID 15124 (C:\Users\<USER>\Downloads\spider-flow-0.5.0\spider-flow-web\target\classes started by 21653 in C:\Users\<USER>\Downloads\spider-flow-0.5.0)
2025-08-17 18:31:56.161 INFO  org.spiderflow.SpiderApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-17 18:31:57.145 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8088 (http)
2025-08-17 18:31:57.152 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8088"]
2025-08-17 18:31:57.159 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-17 18:31:57.159 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-08-17 18:31:57.251 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-17 18:31:57.252 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1024 ms
2025-08-17 18:31:57.349 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-08-17 18:31:57.446 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-08-17 18:31:57.640 ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[org.spiderflow.core.mapper.SpiderFlowMapper.resetNextExecuteTime] is ignored, because it exists, maybe from xml file
2025-08-17 18:31:57.931 INFO  org.spiderflow.core.script.ScriptManager - 成功创建 JavaScript 引擎: graal.js
2025-08-17 18:31:57.932 INFO  org.spiderflow.core.script.ScriptManager - GraalVM JavaScript 引擎已启用 Nashorn 兼容性模式
2025-08-17 18:31:58.697 WARN  org.spiderflow.core.script.ScriptManager - GraalVM JavaScript 引擎注册 _eval 函数失败，将跳过此功能: ReferenceError: Java is not defined in <eval> at line number 1 at column number 14
2025-08-17 18:31:58.783 INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
2025-08-17 18:31:58.791 INFO  org.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-08-17 18:31:58.791 INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v.2.3.2 created.
2025-08-17 18:31:58.792 INFO  org.quartz.simpl.RAMJobStore - RAMJobStore initialized.
2025-08-17 18:31:58.792 INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-08-17 18:31:58.792 INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-08-17 18:31:58.792 INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.3.2
2025-08-17 18:31:58.792 INFO  org.quartz.core.QuartzScheduler - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@7b3085a3
2025-08-17 18:31:59.063 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-08-17 18:31:59.204 INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8088"]
2025-08-17 18:31:59.217 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8088 (http) with context path ''
2025-08-17 18:31:59.218 INFO  o.s.scheduling.quartz.SchedulerFactoryBean - Starting Quartz Scheduler now
2025-08-17 18:31:59.218 INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-08-17 18:31:59.225 INFO  org.spiderflow.SpiderApplication - Started SpiderApplication in 3.567 seconds (JVM running for 4.365)
2025-08-17 18:33:36.048 INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-08-17 18:33:36.065 INFO  o.s.scheduling.quartz.SchedulerFactoryBean - Shutting down Quartz Scheduler
2025-08-17 18:33:36.065 INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-08-17 18:33:36.066 INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-08-17 18:33:36.066 INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-08-17 18:33:36.066 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-08-17 18:33:36.072 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
