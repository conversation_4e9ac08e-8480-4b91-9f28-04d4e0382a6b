2025-08-17 18:31:56.154 INFO  org.spiderflow.SpiderApplication - Starting SpiderApplication using Java 21.0.8 on XIAO with PID 15124 (C:\Users\<USER>\Downloads\spider-flow-0.5.0\spider-flow-web\target\classes started by 21653 in C:\Users\<USER>\Downloads\spider-flow-0.5.0)
2025-08-17 18:31:56.161 INFO  org.spiderflow.SpiderApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-17 18:31:57.145 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8088 (http)
2025-08-17 18:31:57.152 INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8088"]
2025-08-17 18:31:57.159 INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-17 18:31:57.159 INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-08-17 18:31:57.251 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-17 18:31:57.252 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1024 ms
2025-08-17 18:31:57.349 INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-08-17 18:31:57.446 INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-08-17 18:31:57.640 ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[org.spiderflow.core.mapper.SpiderFlowMapper.resetNextExecuteTime] is ignored, because it exists, maybe from xml file
2025-08-17 18:31:57.931 INFO  org.spiderflow.core.script.ScriptManager - 成功创建 JavaScript 引擎: graal.js
2025-08-17 18:31:57.932 INFO  org.spiderflow.core.script.ScriptManager - GraalVM JavaScript 引擎已启用 Nashorn 兼容性模式
