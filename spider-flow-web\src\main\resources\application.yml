# 服务器配置
server:
  port: 8088

# 日志配置
logging:
  level:
    root: INFO
    # org.spiderflow: DEBUG

# 爬虫平台配置
spider:
  # 平台最大线程数
  thread:
    max: 64
    # 单任务默认最大线程数
    default: 8
  # 设置为true时定时任务才生效
  job:
    enable: false
  # 爬虫任务的工作空间
  workspace: /data/spider
  # 布隆过滤器配置
  bloomfilter:
    # 布隆过滤器默认容量
    capacity: 1000000
    # 布隆过滤器默认容错率
    error-rate: 0.0001
  # 死循环检测(节点执行次数超过该值时认为是死循环)默认值为5000
  # detect:
  #   dead-cycle: 5000
  # 爬虫通知相关内容配置,可使用SpiderFlow中的变量名和以下变量名:currentDate:当前发送时间
  notice:
    subject: spider-flow流程通知
    content:
      start: 流程开始执行：{name}，开始时间：{currentDate}
      end: 流程执行完毕：{name}，结束时间：{currentDate}
      exception: 流程发生异常：{name}，异常时间：{currentDate}

# Spring 配置
spring:
  # Jackson 配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    serialization:
      fail_on_empty_beans: false
  
  # MVC 配置
  mvc:
    favicon:
      enabled: false
  
  # 数据源配置
  datasource:
    driver-class-name: com.mysql.jdbc.Driver
    username: root
    password: "021026"
    url: *************************************************************************************************************
  
  # 邮件发送配置
  mail:
    protocol: smtp
    host: smtp.qq.com
    port: 587
    username: <EMAIL>
    password: msrmsjbvaizeecig
    default-encoding: UTF-8
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
            required: true
          ssl:
            enable: false
  
  # 自动配置排除
  autoconfigure:
    exclude: org.springframework.boot.autoconfigure.data.mongo.MongoDataAutoConfiguration,org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration

# Selenium 插件配置
selenium:
  driver:
    # 设置Edge的WebDriver驱动路径，下载地址：https://developer.microsoft.com/en-us/microsoft-edge/tools/webdriver/
    edge: C:\Program Files (x86)\Microsoft\Edge\Application\139.0.3405.86\msedgedriver.exe